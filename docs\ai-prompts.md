Below is a recipe for turning each of your raw Markdown-tool profiles into a fully populated JSON record (like your “InstaClip AI” example), all in that same irreverent, no-BS “AI Dude” voice. The flow is:

    Decide on the input structure of your .md files (e.g. title, summary, bullet lists, etc.).

    Write a ChatGPT prompt template that (a) ingests one .md’s contents, (b) tells the model exactly how to fill each JSON field, and (c) enforces the witty/irreverent tone.

    Loop over all your .md files in a small Node.js (or Python) script:

        Read .md → insert into the prompt template

        Send to OpenAI’s API (e.g. GPT-4.1) with “system” instructions = your AI Dude persona + JSON schema, then “user” = the raw Markdown.

        Parse the JSON string returned, save it as tool-slug.json in your /data/ai-directory/ folder.

Below is a step-by-step guide, plus a sample prompt you can copy-paste and adjust.
1. Define Your Markdown Input Convention

To get predictable results, each .md should follow a simple convention. For example:

# Tool Name

A one-sentence summary of what this tool does.

---

## Detailed Description

(One or more paragraphs explaining it.)

---

## Key Features

- Feature A
- Feature B
- Feature C

---

## Pros

- Pro 1
- Pro 2
- …

## Cons

- Con 1
- Con 2
- …

---

## Pricing

Freemium: 7-day trial, then \$19/mo.

---

## Categories

Primary: “Data Labeling”  
Secondary: “Computer Vision”

---

## Sample Q&A

**Q:** How do I get started?  
**A:** Just upload a CSV, hit “Train,” and you’re off to the races.

**Q:** Is it open-source?  
**A:** Nope—closed-source, but they give you a dev sandbox.

---

## Tags

`#data-labeling` `#computer-vision` `#mlops`

---

## Tooltip

“Train your own CV model in 60 seconds—no PhD required.”

---

## Haiku

Upload and learn fast—  
Models spin up like hotcakes,  
Data dreams come true.

---

## SEO Keywords

`machine learning labeling`, `CV annotation tool`, `AI data pipeline`

---

## Releases

- v1.2 (2025-04-20): Added Web UI, faster export.  
- v1.0 (2024-11-05): Initial release, CLI + Python SDK.

Notes:

    Use consistent headings (## Key Features, ## Pros, etc.).

    Keep each section clearly delineated with --- or consistent headings so that your prompt can “chunk” by heading.

    If some section (e.g. “Haiku”) is optional, just omit that header—your prompt can default to “No haiku provided” or skip that field.

2. Build a Prompt Template That Forces the JSON Structure

Your GPT prompt needs to:

    Load the raw Markdown

    Map each section to the exact JSON fields

    Instruct “AI Dude” to write in irreverent, witty, no-BS tone

    Output ONLY valid JSON (no extra text, no code fences)

Below is a reusable template. You’ll substitute {{RAW_MARKDOWN}} with the contents of each .md.

    System Message (in your API call):

    You are “AI Dude,” the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

    {
      "toolName": string,
      "toolDescription": string,
      "detailedDescription": string,
      "keyFeatures": [string],
      "prosAndCons": {
        "pros": [string],
        "cons": [string]
      },
      "pricingType": string,
      "pricingDetails": string,
      "categories": {
        "primary": string,
        "secondary": string,
        "confidence": number     // a value between 0.0 and 1.0
      },
      "sampleQA": [
        {
          "question": string,
          "answer": string
        }
      ],
      "tags": [string],
      "tooltip": string,
      "haiku": string,
      "seoKeywords": [string],
      "releases": [
        {
          "version": string,
          "releaseDate": string,   // use ISO format YYYY-MM-DD
          "changes": [string]
        }
      ]
    }

    **Tone rules:**
    - Always write like a snarky, witty “AI Dude.”  
    - Keep it punchy: no corporate sugarcoating.  
    - Use contractions, slang, and street-smart humor.  
    - Never apologize or say “I’m sorry.”  
    - Write “toolDescription” as a one-sentence hook.  
    - Ensure “confidence” is 0.90 or above if the category mapping is obvious; if you’re guessing, use 0.80 or 0.75.  

    **VERY IMPORTANT:**  
    - Output exactly one JSON object.  
    - Do not wrap it in backticks or code fences.  
    - Do not add extra fields or comments.  
    - If any section is missing in the Markdown, leave that JSON field as an empty string (`""`), empty array (`[]`), or appropriate default (`"unknown"`).  
    - Always format dates as `YYYY-MM-DD`.  

    Now read the user content and produce the JSON.  

    User Message (the “prompt payload”):

    {{RAW_MARKDOWN}}

When you send that to ChatGPT (e.g. GPT-4.1), the model will parse out each heading from your Markdown and fill the JSON accordingly in “AI Dude” voice.
3. Example Prompt + Response

Below is a condensed example. Suppose sample-tool.md contains:

# SnarkyAI

An unapologetic AI tool that writes blog posts faster than you can type “coffee.”

---

## Detailed Description

SnarkyAI is for people who can’t stand fluff. It generates long-form blog posts by analyzing your outline and spitting out paragraphs that bite. It pulls in trending memes, makes jokes at your competition’s expense, and keeps your SEO on point—so that your readers either love you or hate you, but they can’t ignore you.

---

## Key Features

- Outline-to-Publish: Drop in bullet points, get a 2,000-word article in minutes.  
- SnarkTone Adjuster: Dial the sarcasm up or down from “mild troll” to “full roast.”  
- Meme Inserter: Auto-embed relevant memes or GIFs if you ask nicely.  

---

## Pros

- Saves time on writer’s block—your brain can go watch Netflix.  
- Hilariously irreverent; great for edgy brands.  
- Built-in SEO keywords and meta tags—no more guesswork.  

## Cons

- Not for serious academic stuff; it will roast your thesis.  
- Meme Inserter sometimes posts the wrong GIF—oops.  
- Pricing is per word, so if you forget to set a limit, expect sticker shock.

---

## Pricing

Paid: \$0.02/word; monthly cap of \$100.

---

## Categories

Primary: “Content Generation”  
Secondary: “Marketing Automation”

---

## Sample Q&A

**Q:** Will this replace my copywriter?  
**A:** If your copywriter enjoys binge-watching Netflix all day, maybe. Otherwise, let’s just say you’ll never be short on snark.

**Q:** Can I tone down the sarcasm for work emails?  
**A:** Sure—just set SnarkTone to “professional.” It’ll behave… mostly.

---

## Tags

`#blogging`, `#copywriting`, `#sarcasm`

---

## Tooltip

“Stop typing. Start roasting. Blog like a boss.”

---

## Haiku

Sarcasm unleashed—  
Paragraphs drip neon fire,  
Your readers cringe-laugh.

---

## SEO Keywords

`snark AI`, `blog post generator`, `sarcastic writing AI`

---

## Releases

- v1.1 (2025-01-15): Added “Meme Inserter” and SnarkTone toggle.  
- v1.0 (2024-10-30): Launch with Outline-to-Publish core.

If you send that through the above prompt template, GPT-4.1 might respond:

{
  "toolName": "SnarkyAI",
  "toolDescription": "An unapologetic AI tool that writes blog posts faster than you can type “coffee.”",
  "detailedDescription": "SnarkyAI is for people who can’t stand fluff. It generates long-form blog posts by analyzing your outline and spitting out paragraphs that bite. It pulls in trending memes, makes jokes at your competition’s expense, and keeps your SEO on point—so that your readers either love you or hate you, but they can’t ignore you.",
  "keyFeatures": [
    "Outline-to-Publish: Drop in bullet points, get a 2,000-word article in minutes.",
    "SnarkTone Adjuster: Dial the sarcasm up or down from “mild troll” to “full roast.”",
    "Meme Inserter: Auto-embed relevant memes or GIFs if you ask nicely."
  ],
  "prosAndCons": {
    "pros": [
      "Saves time on writer’s block—your brain can go watch Netflix.",
      "Hilariously irreverent; great for edgy brands.",
      "Built-in SEO keywords and meta tags—no more guesswork."
    ],
    "cons": [
      "Not for serious academic stuff; it will roast your thesis.",
      "Meme Inserter sometimes posts the wrong GIF—oops.",
      "Pricing is per word, so if you forget to set a limit, expect sticker shock."
    ]
  },
  "pricingType": "Paid",
  "pricingDetails": "$0.02/word; monthly cap of $100.",
  "categories": {
    "primary": "Content Generation",
    "secondary": "Marketing Automation",
    "confidence": 0.95
  },
  "sampleQA": [
    {
      "question": "Will this replace my copywriter?",
      "answer": "If your copywriter enjoys binge-watching Netflix all day, maybe. Otherwise, let’s just say you’ll never be short on snark."
    },
    {
      "question": "Can I tone down the sarcasm for work emails?",
      "answer": "Sure—just set SnarkTone to “professional.” It’ll behave… mostly."
    }
  ],
  "tags": [
    "#blogging",
    "#copywriting",
    "#sarcasm"
  ],
  "tooltip": "Stop typing. Start roasting. Blog like a boss.",
  "haiku": "Sarcasm unleashed—\nParagraphs drip neon fire,\nYour readers cringe-laugh.",
  "seoKeywords": [
    "snark AI",
    "blog post generator",
    "sarcastic writing AI"
  ],
  "releases": [
    {
      "version": "v1.1",
      "releaseDate": "2025-01-15",
      "changes": [
        "Added “Meme Inserter” and SnarkTone toggle."
      ]
    },
    {
      "version": "v1.0",
      "releaseDate": "2024-10-30",
      "changes": [
        "Launch with Outline-to-Publish core."
      ]
    }
  ]
}

Notice how:

    Each heading in the Markdown got mapped to a JSON field.

    The tone is snarky (e.g. “If your copywriter enjoys binge-watching Netflix…”).

    The output is pure JSON with no extra commentary or code fences.

4. Automate at Scale with Node.js

Below is a sample Node.js script (scripts/md-to-json.js) that:

    Reads every .md from data/markdown/.

    Constructs the prompt (inserting the “system” + “user” messages).

    Calls OpenAI (e.g. GPT-4.1).

    Parses the JSON response and writes each result to data/ai-directory/<toolSlug>.json.

/**
 * scripts/md-to-json.js
 *
 * Reads all `.md` in data/markdown/
 * For each, sends the content to GPT-4.1 with the “AI Dude” prompt.
 * Saves a JSON file in data/ai-directory/<slug>.json.
 */

import fs from 'fs/promises';
import path from 'path';
import glob from 'glob';
import { OpenAI } from "openai"; // npm install openai

// 1) CONFIG: adjust these paths if needed
const MD_DIR = path.join(process.cwd(), "data", "markdown");
const OUTPUT_DIR = path.join(process.cwd(), "data", "ai-directory");

// 2) YOUR OPENAI API KEY must be set as environment variable: OPENAI_API_KEY
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// 3) System prompt (the “AI Dude” instructions)
const systemPrompt = `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

{
  "toolName": string,
  "toolDescription": string,
  "detailedDescription": string,
  "keyFeatures": [string],
  "prosAndCons": {
    "pros": [string],
    "cons": [string]
  },
  "pricingType": string,
  "pricingDetails": string,
  "categories": {
    "primary": string,
    "secondary": string,
    "confidence": number
  },
  "sampleQA": [
    {
      "question": string,
      "answer": string
    }
  ],
  "tags": [string],
  "tooltip": string,
  "haiku": string,
  "seoKeywords": [string],
  "releases": [
    {
      "version": string,
      "releaseDate": string,
      "changes": [string]
    }
  ]
}

Tone rules:
- Always write like a snarky “AI Dude.”
- Write “toolDescription” as a one-sentence hook.
- If a section is missing in the Markdown, use an empty string, empty array, or `"unknown"`.
- Output exactly one JSON object. No backticks or extra text.`;

(async () => {
  // 4) Ensure output folder exists
  await fs.mkdir(OUTPUT_DIR, { recursive: true });

  // 5) Find all .md files
  const mdFiles = glob.sync(path.join(MD_DIR, "*.md"));

  for (const fullPath of mdFiles) {
    try {
      const rawMd = await fs.readFile(fullPath, "utf-8");
      const filename = path.basename(fullPath, ".md");
      const slug = filename // you can optionally slugify further if needed
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^[-]+|[-]+$/g, "");

      // 6) Send to OpenAI
      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini", // or "gpt-4o" / "gpt-4o-pro" / "gpt-4.1" depending on your access
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: rawMd }
        ],
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: 1500  // adjust if your JSON is larger
      });

      const aiContent = response.choices[0].message.content.trim();

      // 7) Validate it's valid JSON
      let parsed;
      try {
        parsed = JSON.parse(aiContent);
      } catch (err) {
        console.error(`\n❌ JSON parse error for ${slug}:\n`, aiContent);
        continue; // skip or log and proceed to next file
      }

      // 8) Write JSON to disk
      const outPath = path.join(OUTPUT_DIR, `${slug}.json`);
      await fs.writeFile(outPath, JSON.stringify(parsed, null, 2), "utf-8");
      console.log("✅ Wrote", outPath);

    } catch (err) {
      console.error("Error processing", fullPath, err);
    }
  }
})();

Notes on configuration:

    Model choice: If you have GPT-4.1 access, you’ll get best reliability. If you only have GPT-4o or GPT-4o-mini, adjust the model parameter accordingly.

    Temperature/top_p: Keeping temperature around 0.6–0.8 preserves some “flavor” without hallucinating structure.

    max_tokens: Make sure it’s large enough to cover your full JSON (if your “detailedDescription” is long, you may need to bump this up).

To run:

export OPENAI_API_KEY="sk-…"
node scripts/md-to-json.js

After it finishes, you’ll have one JSON file per .md in data/ai-directory/.
5. Tips for Reliable, Consistent Outputs

    Consistent Headings

        Use exactly ## Key Features, ## Pros, ## Cons, etc.

        If your Markdown occasionally uses variations (e.g. “### Features” instead), the LLM may mis-map. Stick to a single convention.

    Always Provide Default Values

        If your tool has no “Haiku,” it’s OK to leave that section out. The prompt says “empty string or empty array,” so GPT-4.1 will fill "haiku": "".

        If you know in advance that a section is missing, you can even append a small note at the bottom of your .md:

    <!-- NO_SampleQA -->

    Then update the prompt to interpret that tag as “no Q&A,” which forces an empty array.

Limit Length of Each Section

    If “Detailed Description” runs 5 000 words, the model might summarize or drop sections. Try to cap each section at 300–500 words to stay well within the max_tokens budget.

    Alternatively, break very long descriptions into multiple paragraphs but still under ~1 000 tokens total.

Validate JSON Immediately

    Always JSON.parse(...) as soon as you get the response. If it fails, log the raw content and tweak your prompt.

    You can add a tiny “JSON validator” step in your Node/Python loop: if it’s invalid, re-send with:

    “Your last output was not valid JSON. Please reformat to valid JSON only, following the given schema exactly.” 

    followed by the same payload.

Adjust “confidence” Logic

    In the system instructions, you told ChatGPT to pick a confidence score. If it’s clearly “Primary: X” and “Secondary: Y,” it will assign 0.95. If it guesses from context, maybe it picks 0.80. You can tune that threshold or even post-process: e.g. “if secondary = ‘unknown’, set confidence to 1.0.”

Post-Processing (Optional)

    After you save all JSON files, you might run a quick script to ensure every object has exactly those keys (no extras).

    If you want to build an index (e.g. a single index.json listing all tool names, categories, and confidence), you can loop over data/ai-directory/*.json and assemble a small array.